part of 'home_bloc.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object> get props => [];
}

class HomePageChanged extends HomeEvent {
  final int index;
  const HomePageChanged(this.index);

  @override
  List<Object> get props => [index];
}

class HomeResetToFirst extends HomeEvent {
  const HomeResetToFirst();
}

class AcceptUser extends HomeEvent {
  final UserData user;
  const AcceptUser(this.user);

  @override
  List<Object> get props => [user];
}

class RejectUser extends HomeEvent {
  final UserData user;
  const RejectUser(this.user);

  @override
  List<Object> get props => [user];
}

class StartPulseAnimation extends HomeEvent {}

class StopPulseAnimation extends HomeEvent {}

class CarouselPageChanged extends HomeEvent {
  final int index;
  const CarouselPageChanged(this.index);

  @override
  List<Object> get props => [index];
}


class CheckTutorialStatus extends HomeEvent {
  const CheckTutorialStatus();
}

class DismissTutorial extends HomeEvent {
  const DismissTutorial();
}

class InitializeSwipableController extends HomeEvent {
  const InitializeSwipableController();
}

class DisposeSwipableController extends HomeEvent {
  const DisposeSwipableController();
}
