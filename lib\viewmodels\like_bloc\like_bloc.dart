// ignore: depend_on_referenced_packages
import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';

part 'like_event.dart';
part 'like_state.dart';

class LikeBloc extends Bloc<LikeEvent, LikeState> {
  final PageController pageController = PageController();
  LikeBloc() : super(LikeState()) {
    on<TabChangedEvent>(_onTabChangedEvent);
  }
  void _onTabChangedEvent(TabChangedEvent event, Emitter<LikeState> emit) {
    pageController.jumpToPage(event.newIndex);
    emit(state.copyWith(curentTebIndex: event.newIndex));
  }

  @override
  Future<void> close() {
    pageController.dispose();
    return super.close();
  }
}
