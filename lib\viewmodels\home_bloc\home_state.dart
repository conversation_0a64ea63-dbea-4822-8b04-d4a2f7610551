part of 'home_bloc.dart';

class HomeState extends Equatable {
  final List<UserData> users;
  final int currentIndex;
  final PageController pageController;
  final bool isPulseAnimating;
  final int carouselCurrentIndex;
  final bool hasSeenTutorial;
  final SwipableStackController? swipableController;

  const HomeState({
    required this.users,
    required this.currentIndex,
    required this.pageController,
    this.isPulseAnimating = false,
    this.carouselCurrentIndex = 0,
    this.hasSeenTutorial = true,
    this.swipableController,
  });

  HomeState copyWith({
    List<UserData>? users,
    int? currentIndex,
    PageController? pageController,
    bool? isPulseAnimating,
    int? carouselCurrentIndex,
    bool? hasSeenTutorial,
    SwipableStackController? swipableController,
  }) {
    return HomeState(
      users: users ?? this.users,
      currentIndex: currentIndex ?? this.currentIndex,
      pageController: pageController ?? this.pageController,
      isPulseAnimating: isPulseAnimating ?? this.isPulseAnimating,
      carouselCurrentIndex: carouselCurrentIndex ?? this.carouselCurrentIndex,
      hasSeenTutorial: hasSeenTutorial ?? this.hasSeenTutorial,
      swipableController: swipableController ?? this.swipableController,
    );
  }

  @override
  List<Object?> get props => [
    users,
    currentIndex,
    isPulseAnimating,
    carouselCurrentIndex,
    hasSeenTutorial,
    swipableController,
  ];
}
