class UserProfile {
  final int? id;
  final String? fullName;
  final String? dob;
  final String? gender;
  final String? preferedGender;
  final String? preferedSmoking;
  final String? cleaniness;
  final String? isHavingPet;
  final String? classStanding;
  final String? habitsLifestyle;
  final String? livingStyle;
  final String? interestsHobbies;
  final String? about;
  final String? contactNumber;
  final String? preferedLeasePeriod;
  final String? preferedLocations;
  final String? personalityTypeDescription;
  final bool? isVerified;
  final bool? isActive;
  final String? updatedAt;
  final String? profilePicture;
  final List<String>? profilePictures;

  UserProfile({
    this.id,
    this.fullName,
    this.dob,
    this.gender,
    this.preferedGender,
    this.preferedSmoking,
    this.cleaniness,
    this.isHavingPet,
    this.classStanding,
    this.habitsLifestyle,
    this.livingStyle,
    this.interestsHobbies,
    this.about,
    this.contactNumber,
    this.preferedLeasePeriod,
    this.preferedLocations,
    this.personalityTypeDescription,
    this.isVerified,
    this.isActive,
    this.updatedAt,
    this.profilePicture,
    this.profilePictures,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] as int?,
      fullName: json['full_name'] as String?,
      dob: json['dob'] as String?,
      gender: json['gender'] as String?,
      preferedGender: json['prefered_gender'] as String?,
      preferedSmoking: json['prefered_smoking'] as String?,
      cleaniness: json['cleaniness'] as String?,
      isHavingPet: json['is_having_pet']?.toString(),
      classStanding: json['class_standing'] as String?,
      habitsLifestyle: json['habits_lifestyle'] as String?,
      livingStyle: json['living_style'] as String?,
      interestsHobbies: json['interests_hobbies'] as String?,
      about: json['about'] as String?,
      contactNumber: json['contact_number'] as String?,
      preferedLeasePeriod: json['prefered_lease_period'] as String?,
      preferedLocations: json['prefered_locations'] as String?,
      personalityTypeDescription: json['personality_type_description'] as String?,
      isVerified: json['is_verified'] as bool?,
      isActive: json['is_active'] as bool?,
      updatedAt: json['updated_at'] as String?,
      profilePicture: json['profile_picture'] as String?,
      profilePictures: (json['profile_pictures'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'dob': dob,
      'gender': gender,
      'prefered_gender': preferedGender,
      'prefered_smoking': preferedSmoking,
      'cleaniness': cleaniness,
      'is_having_pet': isHavingPet,
      'class_standing': classStanding,
      'habits_lifestyle': habitsLifestyle,
      'living_style': livingStyle,
      'interests_hobbies': interestsHobbies,
      'about': about,
      'contact_number': contactNumber,
      'prefered_lease_period': preferedLeasePeriod,
      'prefered_locations': preferedLocations,
      'personality_type_description': personalityTypeDescription,
      'is_verified': isVerified,
      'is_active': isActive,
      'updated_at': updatedAt,
      'profile_picture': profilePicture,
      'profile_pictures': profilePictures,
    };
  }
}
