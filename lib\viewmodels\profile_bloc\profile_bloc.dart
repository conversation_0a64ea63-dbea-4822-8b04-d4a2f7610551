import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/utils/image_picker_utils.dart';

part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  Timer? _timer;

  ProfileBloc() : super(ProfileState.initial()) {
    // Form Fields
    on<FullProfileNameChanged>(_onFullNameChanged);
    on<NameChanged>(_onNameChanged);
    on<PhoneChanged>(_onPhoneChanged);
    on<EmailChanged>(_onEmailChanged);
    on<ProfileDateOfBirthChanged>(_onDateOfBirthChanged);
    on<GenderChanged>(_onGenderChanged);
    on<AgeChanged>(_onAgeChanged);
    on<LeasePeriodChanged>(_onLeasePeriodChanged);
    on<ContactNumberChanged>(_onContactNumberChanged);
    on<SelectPreferredLocations>(_onSelectPreferredLocations);
    on<AboutChanged>(_onAboutChanged);

    // Profile submission
    on<ProfileSubmitted>(_onProfileSubmitted);
    on<FinalProfileSubmitted>(_onFinalProfileSubmitted);

    // Photos
    on<SelectUserProfile>(_onSelectUserProfile);
    on<ProfileImageChanged>(_onProfileImageChanged);
    on<AddPhoto>(_onAddPhoto);
    on<RemovePhoto>(_onRemovePhoto);
    on<SelectMultiplePhotos>(_onSelectMultiplePhotos);

    // Personality Tags
    on<AddPersonalityTag>(_onAddPersonalityTag);
    on<RemovePersonalityTag>(_onRemovePersonalityTag);
    on<AddCustomPersonalityTag>(_onAddCustomPersonalityTag);

    // Selection Events
    on<SelectGender>(_onSelectGender);
    on<SelectPreferredGender>(_onSelectPreferredGender);
    on<SelectPeriod>(_onSelectPeriod);
    on<SelectSmokingPerson>(_onSelectSmokingPerson);
    on<SelectCleanLevel>(_onSelectCleanLevel);
    on<SelectPet>(_onSelectPet);
    on<SelectClassStand>(_onSelectClassStand);
    on<SelectHabitsAndLifestyle>(_onSelectHabitsAndLifestyle);
    on<SelectCleanlinessLivingStyle>(_onSelectCleanlinessLivingStyle);
    on<SelectInterestsHobbies>(_onSelectInterestsHobbies);
    on<TogglePickThings>(_onTogglePickThings);

    // Misc
    on<SearchChanged>(_onSearchChanged);
    on<SaveProfile>(_onSaveProfile);
  }

  // ========== Event Handlers ==========

  Future<void> _onProfileSubmitted(
    ProfileSubmitted event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));
      // RoomEight.show(
      //   message: "Login successfully.",
      //   type: ToastificationType.success,
      // );
      Logger.lOG("ok");
      NavigatorService.pushNamed(AppRoutes.addPersonalDetailScreen2);
      // NavigatorService.pushNamed(AppRoutes.settingScreen);
      emit(state.copyWith(isloginLoading: false));
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isloginLoading: false));
    }
  }

  // Add new event handler for final profile submission
  Future<void> _onFinalProfileSubmitted(
    FinalProfileSubmitted event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));

      // Prepare data for API call
      final profileData = _prepareProfileData();

      // Debug: Print the data being sent
      Logger.lOG("Profile data being sent:");
      profileData.forEach((key, value) {
        Logger.lOG("$key: $value");
      });

      // Call the profile detail API
      final authRepository = AuthRepository(apiClient: ApiClient());
      await authRepository.profileDetailCall(data: profileData);

      Logger.lOG("Profile submitted successfully");
      NavigatorService.pushNamed(AppRoutes.roomEightNavBar);
      emit(state.copyWith(isloginLoading: false));
    } catch (e) {
      Logger.lOG("Profile submission error: ${e.toString()}");
      emit(state.copyWith(isloginLoading: false));
    }
  }

  // Helper method to prepare profile data for API
  Map<String, dynamic> _prepareProfileData() {
    final fullName = state.fullNameController?.text.trim() ?? "App Tester";
    final dob = state.dobController?.text.trim() ?? "09/07/2007";
    final contactNumber = state.phoneController.text.trim().isNotEmpty
        ? state.phoneController.text.trim()
        : "1234567890";
    final about = _buildPersonalityDescription().isNotEmpty
        ? _buildPersonalityDescription()
        : "Friendly, Organized, Quiet, Outgoing, Clean, Adventurous";
    final preferedLocations =
        "[{lat : 21.186537, long: 72.810883},{lat : 21.186537, long: 72.810883},{lat : 21.186537, long: 72.810883},]";
    final personalityTypeDescription = about;

    // final validPaths = state.photoPaths
    //     .where((path) => path.isNotEmpty && !path.startsWith('assets/'))
    //     .toList();

    final data = <String, dynamic>{
      "profile_picture": state.userProfile!.path,
      "profile_pictures": state.photoPaths,
      "full_name": fullName,
      "dob": dob,
      "gender": _mapGenderToId(state.selectedGender),
      "prefered_gender": _mapGenderToId(state.selectedPreferredGender),
      "prefered_smoking": _mapSmokingToId(state.selectedSmokingPerson),
      "cleaniness": _mapCleanlinessToId(state.sleectedCleanLevenl),
      "is_having_pet": _mapPetToBoolean(state.selectedPet),
      "class_standing": _mapClassStandingToId(state.selectedClassStand),
      "habits_lifestyle": _mapListToIds(state.selectedHabitsAndLifestyle),
      "living_style": _mapListToIds(state.selectedCleanlinessLivingStyle),
      "interests_hobbies": _mapListToIds(state.selectedInterestsHobbies),
      "about": about,
      "contact_number": contactNumber,
      "prefered_lease_period": state.selectedPeriod ?? "",
      "prefered_locations": preferedLocations,
      "personality_type_description": personalityTypeDescription,
    };

    // Validate required fields
    _validateRequiredFields(data);

    return data;
  }

  // Helper method to validate required fields
  void _validateRequiredFields(Map<String, dynamic> data) {
    final requiredFields = [
      'full_name',
      'dob',
      'gender',
      'prefered_gender',
      'prefered_smoking',
      'cleaniness',
      'is_having_pet',
      'class_standing',
      'habits_lifestyle',
      'living_style',
      'interests_hobbies',
      'about',
      'contact_number',
      'prefered_lease_period',
      'personality_type_description',
    ];

    for (final field in requiredFields) {
      final value = data[field];
      if (value == null || (value is String && value.isEmpty)) {
        Logger.lOG("WARNING: Required field '$field' is missing or empty");
      }
    }
  }

  // Helper method to build personality description from selected tags
  String _buildPersonalityDescription() {
    final allTags = [...state.personalityTags, ...state.customPersonalityTags];
    return allTags.join(", ");
  }

  // Helper methods to map UI values to API format
  String _mapGenderToId(String? gender) {
    switch (gender) {
      case "Male":
        return "1";
      case "Female":
        return "2";
      case "Both":
        return "3";
      default:
        return "1";
    }
  }

  String _mapSmokingToId(String? smoking) {
    switch (smoking) {
      case "Smoker":
        return "1";
      case "Social Smoker":
        return "2";
      case "Occasionally":
        return "3";
      case "Non - Smoker":
        return "4";
      default:
        return "4";
    }
  }

  String _mapCleanlinessToId(String? cleanliness) {
    switch (cleanliness) {
      case "Very Tidy":
        return "1";
      case "Messy":
        return "2";
      case "Average":
        return "3";
      default:
        return "3";
    }
  }

  bool _mapPetToBoolean(String? pet) {
    switch (pet) {
      case "Okay with Pets":
        return true;
      case "Not Okay":
        return false;
      default:
        return true; // Default to true for better user experience
    }
  }

  String _mapClassStandingToId(String? classStanding) {
    switch (classStanding) {
      case "Freshman":
        return "1";
      case "Sophomore":
        return "2";
      case "Junior":
        return "3";
      case "Senior":
        return "4";
      default:
        return "5"; // Any
    }
  }

  String _mapListToIds(List<String> items) {
    // Create proper mappings for lifestyle options
    final Map<String, int> habitsLifestyleMap = {
      'Night Owl': 1,
      'Early Riser': 2,
      'Occasionally': 3,
      'Cats': 4,
      'Coffee': 5,
    };

    final Map<String, int> cleanlinessLivingStyleMap = {
      'Clean Freak': 1,
      'Chill': 2,
      'Environment': 3,
      'Study Focused': 4,
      'Vegetarian': 5,
    };

    final Map<String, int> interestsHobbiesMap = {
      'Gaming': 1,
      'Gym Rat': 2,
      'Art & Crafts': 3,
      'Music Lover': 4,
      'Dancing': 5,
      'Camping': 6,
    };

    // Combine all mappings for lookup
    final allMappings = <String, int>{
      ...habitsLifestyleMap,
      ...cleanlinessLivingStyleMap,
      ...interestsHobbiesMap,
    };

    final ids = <int>[];
    for (final item in items) {
      final id = allMappings[item];
      if (id != null) {
        ids.add(id);
      }
    }
    return "[${ids.join(',')}]";
  }

  void _onFullNameChanged(
    FullProfileNameChanged event,
    Emitter<ProfileState> emit,
  ) {
    state.fullNameController?.text = event.fullName;
    emit(state.copyWith(fullNameController: state.fullNameController));
  }

  void _onNameChanged(NameChanged event, Emitter<ProfileState> emit) {
    state.nameController.text = event.name;
    emit(state.copyWith(nameController: state.nameController));
  }

  void _onPhoneChanged(PhoneChanged event, Emitter<ProfileState> emit) {
    state.phoneController.text = event.phone;
    emit(
      state.copyWith(
        phone: event.phone,
        phoneController: state.phoneController,
      ),
    );
  }

  void _onEmailChanged(EmailChanged event, Emitter<ProfileState> emit) {
    state.emailController.text = event.email;
    emit(state.copyWith(emailController: state.emailController));
  }

  void _onDateOfBirthChanged(
    ProfileDateOfBirthChanged event,
    Emitter<ProfileState> emit,
  ) {
    Logger.lOG("in bloc : ${state.dobController?.text}");
    Logger.lOG("in event : ${event.dateOfBirth}");
    state.dobController?.text = event.dateOfBirth;
    Logger.lOG("in after bloc : ${state.dobController?.text}");
    emit(state.copyWith(dobController: state.dobController));
    Logger.lOG("in bloc : ${state.dobController?.text}");
  }

  void _onGenderChanged(GenderChanged event, Emitter<ProfileState> emit) {
    emit(state.copyWith(gender: event.gender));
  }

  void _onAgeChanged(AgeChanged event, Emitter<ProfileState> emit) {
    emit(state.copyWith(ageController: TextEditingController(text: event.age)));
  }

  void _onLeasePeriodChanged(
    LeasePeriodChanged event,
    Emitter<ProfileState> emit,
  ) {
    state.leasePeriodController.text = event.leasePeriod;
    emit(
      state.copyWith(
        leasePeriod: event.leasePeriod,
        leasePeriodController: state.leasePeriodController,
      ),
    );
  }

  void _onContactNumberChanged(
    ContactNumberChanged event,
    Emitter<ProfileState> emit,
  ) {
    state.contactNumberController.text = event.contactNumber;
    emit(
      state.copyWith(contactNumberController: state.contactNumberController),
    );
  }

  void _onSelectPreferredLocations(
    SelectPreferredLocations event,
    Emitter<ProfileState> emit,
  ) {
    state.preferredLocationsController.text = event.locations;
    emit(
      state.copyWith(
        preferredLocationsController: state.preferredLocationsController,
      ),
    );
  }

  void _onAboutChanged(AboutChanged event, Emitter<ProfileState> emit) {
    state.aboutController.text = event.about;
    emit(state.copyWith(aboutController: state.aboutController));
  }

  Future<void> _onSelectUserProfile(
    SelectUserProfile event,
    Emitter<ProfileState> emit,
  ) async {
    File? pickedImage = await Imagepickerutils.pickImageFromGallery();
    if (pickedImage != null && pickedImage.path.isNotEmpty) {
      emit(state.copyWith(userProfile: pickedImage));
      Logger.lOG("Image picked");
    } else {
      Logger.lOG("Image not picked");
    }
  }

  void _onProfileImageChanged(
    ProfileImageChanged event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(profileImagePath: event.imagePath));
  }

  void _onAddPhoto(AddPhoto event, Emitter<ProfileState> emit) {
    final updatedPhotos = List<String>.from(state.photoPaths)
      ..add(event.photoPath);
    emit(state.copyWith(photoPaths: updatedPhotos));
  }

  void _onRemovePhoto(RemovePhoto event, Emitter<ProfileState> emit) {
    final updatedPhotos = List<String>.from(state.photoPaths)
      ..remove(event.photoPath);
    emit(state.copyWith(photoPaths: updatedPhotos));
  }

  void _onSelectMultiplePhotos(
    SelectMultiplePhotos event,
    Emitter<ProfileState> emit,
  ) {
    final uniquePaths = event.photoPaths.toSet().toList();
    emit(state.copyWith(photoPaths: uniquePaths));
  }

  void _onAddPersonalityTag(
    AddPersonalityTag event,
    Emitter<ProfileState> emit,
  ) {
    final updatedTags = List<String>.from(state.personalityTags)
      ..add(event.tag);
    emit(state.copyWith(personalityTags: updatedTags));
  }

  void _onRemovePersonalityTag(
    RemovePersonalityTag event,
    Emitter<ProfileState> emit,
  ) {
    final updatedTags = List<String>.from(state.personalityTags)
      ..remove(event.tag);
    emit(state.copyWith(personalityTags: updatedTags));
  }

  void _onAddCustomPersonalityTag(
    AddCustomPersonalityTag event,
    Emitter<ProfileState> emit,
  ) {
    if (!state.customPersonalityTags.contains(event.tag)) {
      emit(
        state.copyWith(
          customPersonalityTags: List.from(state.customPersonalityTags)
            ..add(event.tag),
        ),
      );
    }
  }

  void _onSelectGender(SelectGender event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedGender: event.gender));
  }

  void _onSelectPreferredGender(
    SelectPreferredGender event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedPreferredGender: event.preferredGender));
  }

  void _onSelectPeriod(SelectPeriod event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedPeriod: event.period));
  }

  void _onSelectSmokingPerson(
    SelectSmokingPerson event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedSmokingPerson: event.type));
  }

  void _onSelectCleanLevel(SelectCleanLevel event, Emitter<ProfileState> emit) {
    emit(state.copyWith(sleectedCleanLevenl: event.level));
  }

  void _onSelectPet(SelectPet event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedPet: event.pet));
  }

  void _onSelectClassStand(SelectClassStand event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedClassStand: event.classStand));
  }

  void _onSelectHabitsAndLifestyle(
    SelectHabitsAndLifestyle event,
    Emitter<ProfileState> emit,
  ) {
    final habits = List<String>.from(state.selectedHabitsAndLifestyle);
    if (habits.contains(event.habitsAndLifestyle)) {
      habits.remove(event.habitsAndLifestyle);
    } else {
      habits.add(event.habitsAndLifestyle);
    }
    final newCount =
        habits.length +
        state.selectedCleanlinessLivingStyle.length +
        state.selectedInterestsHobbies.length;
    emit(
      state.copyWith(
        selectedHabitsAndLifestyle: habits,
        selectedOption: newCount,
      ),
    );
  }

  void _onSelectCleanlinessLivingStyle(
    SelectCleanlinessLivingStyle event,
    Emitter<ProfileState> emit,
  ) {
    final list = List<String>.from(state.selectedCleanlinessLivingStyle);
    if (list.contains(event.cleanlinessLivingStyle)) {
      list.remove(event.cleanlinessLivingStyle);
    } else {
      list.add(event.cleanlinessLivingStyle);
    }
    final newCount =
        state.selectedHabitsAndLifestyle.length +
        list.length +
        state.selectedInterestsHobbies.length;
    emit(
      state.copyWith(
        selectedCleanlinessLivingStyle: list,
        selectedOption: newCount,
      ),
    );
  }

  void _onSelectInterestsHobbies(
    SelectInterestsHobbies event,
    Emitter<ProfileState> emit,
  ) {
    final hobbies = List<String>.from(state.selectedInterestsHobbies);
    if (hobbies.contains(event.interestsHobbies)) {
      hobbies.remove(event.interestsHobbies);
    } else {
      hobbies.add(event.interestsHobbies);
    }
    final newCount =
        state.selectedHabitsAndLifestyle.length +
        state.selectedCleanlinessLivingStyle.length +
        hobbies.length;
    emit(
      state.copyWith(
        selectedInterestsHobbies: hobbies,
        selectedOption: newCount,
      ),
    );
  }

  void _onTogglePickThings(TogglePickThings event, Emitter<ProfileState> emit) {
    emit(state.copyWith(isPickThings: !state.isPickThings));
  }

  void _onSearchChanged(SearchChanged event, Emitter<ProfileState> emit) {
    // Implement if needed
  }

  void _onSaveProfile(SaveProfile event, Emitter<ProfileState> emit) {
    emit(state); // Save profile locally or to server
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
