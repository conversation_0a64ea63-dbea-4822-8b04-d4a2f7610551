/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// Directory path: assets/images/pngs
  $AssetsImagesPngsGen get pngs => const $AssetsImagesPngsGen();

  /// Directory path: assets/images/svgs
  $AssetsImagesSvgsGen get svgs => const $AssetsImagesSvgsGen();
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/ic_arrow.json
  LottieGenImage get icArrow => const LottieGenImage('assets/lottie/ic_arrow.json');

  /// List of all assets
  List<LottieGenImage> get values => [icArrow];
}

class $AssetsImagesPngsGen {
  const $AssetsImagesPngsGen();

  /// Directory path: assets/images/pngs/icons
  $AssetsImagesPngsIconsGen get icons => const $AssetsImagesPngsIconsGen();

  /// Directory path: assets/images/pngs/other
  $AssetsImagesPngsOtherGen get other => const $AssetsImagesPngsOtherGen();
}

class $AssetsImagesSvgsGen {
  const $AssetsImagesSvgsGen();

  /// Directory path: assets/images/svgs/icons
  $AssetsImagesSvgsIconsGen get icons => const $AssetsImagesSvgsIconsGen();

  /// Directory path: assets/images/svgs/other
  $AssetsImagesSvgsOtherGen get other => const $AssetsImagesSvgsOtherGen();
}

class $AssetsImagesPngsIconsGen {
  const $AssetsImagesPngsIconsGen();

  /// File path: assets/images/pngs/icons/ic_add.png
  AssetGenImage get icAdd => const AssetGenImage('assets/images/pngs/icons/ic_add.png');

  /// File path: assets/images/pngs/icons/ic_arrow_down.png
  AssetGenImage get icArrowDown => const AssetGenImage('assets/images/pngs/icons/ic_arrow_down.png');

  /// File path: assets/images/pngs/icons/ic_arrow_up.png
  AssetGenImage get icArrowUp => const AssetGenImage('assets/images/pngs/icons/ic_arrow_up.png');

  /// File path: assets/images/pngs/icons/ic_art.png
  AssetGenImage get icArt => const AssetGenImage('assets/images/pngs/icons/ic_art.png');

  /// File path: assets/images/pngs/icons/ic_campe.png
  AssetGenImage get icCampe => const AssetGenImage('assets/images/pngs/icons/ic_campe.png');

  /// File path: assets/images/pngs/icons/ic_cansol.png
  AssetGenImage get icCansol => const AssetGenImage('assets/images/pngs/icons/ic_cansol.png');

  /// File path: assets/images/pngs/icons/ic_cat.png
  AssetGenImage get icCat => const AssetGenImage('assets/images/pngs/icons/ic_cat.png');

  /// File path: assets/images/pngs/icons/ic_clean.png
  AssetGenImage get icClean => const AssetGenImage('assets/images/pngs/icons/ic_clean.png');

  /// File path: assets/images/pngs/icons/ic_coffee.png
  AssetGenImage get icCoffee => const AssetGenImage('assets/images/pngs/icons/ic_coffee.png');

  /// File path: assets/images/pngs/icons/ic_dance.png
  AssetGenImage get icDance => const AssetGenImage('assets/images/pngs/icons/ic_dance.png');

  /// File path: assets/images/pngs/icons/ic_earth.png
  AssetGenImage get icEarth => const AssetGenImage('assets/images/pngs/icons/ic_earth.png');

  /// File path: assets/images/pngs/icons/ic_emoj.png
  AssetGenImage get icEmoj => const AssetGenImage('assets/images/pngs/icons/ic_emoj.png');

  /// File path: assets/images/pngs/icons/ic_google.png
  AssetGenImage get icGoogle => const AssetGenImage('assets/images/pngs/icons/ic_google.png');

  /// File path: assets/images/pngs/icons/ic_gym.png
  AssetGenImage get icGym => const AssetGenImage('assets/images/pngs/icons/ic_gym.png');

  /// File path: assets/images/pngs/icons/ic_help.png
  AssetGenImage get icHelp => const AssetGenImage('assets/images/pngs/icons/ic_help.png');

  /// File path: assets/images/pngs/icons/ic_learn.png
  AssetGenImage get icLearn => const AssetGenImage('assets/images/pngs/icons/ic_learn.png');

  /// File path: assets/images/pngs/icons/ic_smoke.png
  AssetGenImage get icSmoke => const AssetGenImage('assets/images/pngs/icons/ic_smoke.png');

  /// File path: assets/images/pngs/icons/ic_song.png
  AssetGenImage get icSong => const AssetGenImage('assets/images/pngs/icons/ic_song.png');

  /// File path: assets/images/pngs/icons/ic_star.png
  AssetGenImage get icStar => const AssetGenImage('assets/images/pngs/icons/ic_star.png');

  /// File path: assets/images/pngs/icons/ic_sun.png
  AssetGenImage get icSun => const AssetGenImage('assets/images/pngs/icons/ic_sun.png');

  /// File path: assets/images/pngs/icons/ic_vage.png
  AssetGenImage get icVage => const AssetGenImage('assets/images/pngs/icons/ic_vage.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    icAdd,
    icArrowDown,
    icArrowUp,
    icArt,
    icCampe,
    icCansol,
    icCat,
    icClean,
    icCoffee,
    icDance,
    icEarth,
    icEmoj,
    icGoogle,
    icGym,
    icHelp,
    icLearn,
    icSmoke,
    icSong,
    icStar,
    icSun,
    icVage,
  ];
}

class $AssetsImagesPngsOtherGen {
  const $AssetsImagesPngsOtherGen();

  /// File path: assets/images/pngs/other/png_app_logo.png
  AssetGenImage get pngAppLogo => const AssetGenImage('assets/images/pngs/other/png_app_logo.png');

  /// File path: assets/images/pngs/other/png_auth_bg1.png
  AssetGenImage get pngAuthBg1 => const AssetGenImage('assets/images/pngs/other/png_auth_bg1.png');

  /// File path: assets/images/pngs/other/png_auth_bg2.png
  AssetGenImage get pngAuthBg2 => const AssetGenImage('assets/images/pngs/other/png_auth_bg2.png');

  /// File path: assets/images/pngs/other/png_auth_bg3.png
  AssetGenImage get pngAuthBg3 => const AssetGenImage('assets/images/pngs/other/png_auth_bg3.png');

  /// File path: assets/images/pngs/other/png_auth_bg4.png
  AssetGenImage get pngAuthBg4 => const AssetGenImage('assets/images/pngs/other/png_auth_bg4.png');

  /// File path: assets/images/pngs/other/png_home_user.png
  AssetGenImage get pngHomeUser => const AssetGenImage('assets/images/pngs/other/png_home_user.png');

  /// File path: assets/images/pngs/other/png_login_bg.png
  AssetGenImage get pngLoginBg => const AssetGenImage('assets/images/pngs/other/png_login_bg.png');

  /// File path: assets/images/pngs/other/png_onboarding_bg1.png
  AssetGenImage get pngOnboardingBg1 => const AssetGenImage('assets/images/pngs/other/png_onboarding_bg1.png');

  /// File path: assets/images/pngs/other/png_onboarding_bg2.png
  AssetGenImage get pngOnboardingBg2 => const AssetGenImage('assets/images/pngs/other/png_onboarding_bg2.png');

  /// File path: assets/images/pngs/other/png_onboarding_container.png
  AssetGenImage get pngOnboardingContainer => const AssetGenImage('assets/images/pngs/other/png_onboarding_container.png');

  /// File path: assets/images/pngs/other/png_signup_bg.png
  AssetGenImage get pngSignupBg => const AssetGenImage('assets/images/pngs/other/png_signup_bg.png');

  /// File path: assets/images/pngs/other/png_splash_bg.png
  AssetGenImage get pngSplashBg => const AssetGenImage('assets/images/pngs/other/png_splash_bg.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    pngAppLogo,
    pngAuthBg1,
    pngAuthBg2,
    pngAuthBg3,
    pngAuthBg4,
    pngHomeUser,
    pngLoginBg,
    pngOnboardingBg1,
    pngOnboardingBg2,
    pngOnboardingContainer,
    pngSignupBg,
    pngSplashBg,
  ];
}

class $AssetsImagesSvgsIconsGen {
  const $AssetsImagesSvgsIconsGen();

  /// File path: assets/images/svgs/icons/ic_alarm.svg
  SvgGenImage get icAlarm => const SvgGenImage('assets/images/svgs/icons/ic_alarm.svg');

  /// File path: assets/images/svgs/icons/ic_arrow_right.svg
  SvgGenImage get icArrowRight => const SvgGenImage('assets/images/svgs/icons/ic_arrow_right.svg');

  /// File path: assets/images/svgs/icons/ic_back_arrow.svg
  SvgGenImage get icBackArrow => const SvgGenImage('assets/images/svgs/icons/ic_back_arrow.svg');

  /// File path: assets/images/svgs/icons/ic_be_kind.svg
  SvgGenImage get icBeKind => const SvgGenImage('assets/images/svgs/icons/ic_be_kind.svg');

  /// File path: assets/images/svgs/icons/ic_block_account.svg
  SvgGenImage get icBlockAccount => const SvgGenImage('assets/images/svgs/icons/ic_block_account.svg');

  /// File path: assets/images/svgs/icons/ic_calender.svg
  SvgGenImage get icCalender => const SvgGenImage('assets/images/svgs/icons/ic_calender.svg');

  /// File path: assets/images/svgs/icons/ic_chat.svg
  SvgGenImage get icChat => const SvgGenImage('assets/images/svgs/icons/ic_chat.svg');

  /// File path: assets/images/svgs/icons/ic_chat_fill.svg
  SvgGenImage get icChatFill => const SvgGenImage('assets/images/svgs/icons/ic_chat_fill.svg');

  /// File path: assets/images/svgs/icons/ic_close.svg
  SvgGenImage get icClose => const SvgGenImage('assets/images/svgs/icons/ic_close.svg');

  /// File path: assets/images/svgs/icons/ic_dropdown.svg
  SvgGenImage get icDropdown => const SvgGenImage('assets/images/svgs/icons/ic_dropdown.svg');

  /// File path: assets/images/svgs/icons/ic_edit.svg
  SvgGenImage get icEdit => const SvgGenImage('assets/images/svgs/icons/ic_edit.svg');

  /// File path: assets/images/svgs/icons/ic_eye_off.svg
  SvgGenImage get icEyeOff => const SvgGenImage('assets/images/svgs/icons/ic_eye_off.svg');

  /// File path: assets/images/svgs/icons/ic_eye_on.svg
  SvgGenImage get icEyeOn => const SvgGenImage('assets/images/svgs/icons/ic_eye_on.svg');

  /// File path: assets/images/svgs/icons/ic_forward_arrow.svg
  SvgGenImage get icForwardArrow => const SvgGenImage('assets/images/svgs/icons/ic_forward_arrow.svg');

  /// File path: assets/images/svgs/icons/ic_heart.svg
  SvgGenImage get icHeart => const SvgGenImage('assets/images/svgs/icons/ic_heart.svg');

  /// File path: assets/images/svgs/icons/ic_heart_fill.svg
  SvgGenImage get icHeartFill => const SvgGenImage('assets/images/svgs/icons/ic_heart_fill.svg');

  /// File path: assets/images/svgs/icons/ic_help.svg
  SvgGenImage get icHelp => const SvgGenImage('assets/images/svgs/icons/ic_help.svg');

  /// File path: assets/images/svgs/icons/ic_home.svg
  SvgGenImage get icHome => const SvgGenImage('assets/images/svgs/icons/ic_home.svg');

  /// File path: assets/images/svgs/icons/ic_home_fill.svg
  SvgGenImage get icHomeFill => const SvgGenImage('assets/images/svgs/icons/ic_home_fill.svg');

  /// File path: assets/images/svgs/icons/ic_home_tag.svg
  SvgGenImage get icHomeTag => const SvgGenImage('assets/images/svgs/icons/ic_home_tag.svg');

  /// File path: assets/images/svgs/icons/ic_keep_it_real.svg
  SvgGenImage get icKeepItReal => const SvgGenImage('assets/images/svgs/icons/ic_keep_it_real.svg');

  /// File path: assets/images/svgs/icons/ic_like.svg
  SvgGenImage get icLike => const SvgGenImage('assets/images/svgs/icons/ic_like.svg');

  /// File path: assets/images/svgs/icons/ic_lock.svg
  SvgGenImage get icLock => const SvgGenImage('assets/images/svgs/icons/ic_lock.svg');

  /// File path: assets/images/svgs/icons/ic_locks.svg
  SvgGenImage get icLocks => const SvgGenImage('assets/images/svgs/icons/ic_locks.svg');

  /// File path: assets/images/svgs/icons/ic_logout.svg
  SvgGenImage get icLogout => const SvgGenImage('assets/images/svgs/icons/ic_logout.svg');

  /// File path: assets/images/svgs/icons/ic_mail.svg
  SvgGenImage get icMail => const SvgGenImage('assets/images/svgs/icons/ic_mail.svg');

  /// File path: assets/images/svgs/icons/ic_male.svg
  SvgGenImage get icMale => const SvgGenImage('assets/images/svgs/icons/ic_male.svg');

  /// File path: assets/images/svgs/icons/ic_mic.svg
  SvgGenImage get icMic => const SvgGenImage('assets/images/svgs/icons/ic_mic.svg');

  /// File path: assets/images/svgs/icons/ic_notification.svg
  SvgGenImage get icNotification => const SvgGenImage('assets/images/svgs/icons/ic_notification.svg');

  /// File path: assets/images/svgs/icons/ic_notifications.svg
  SvgGenImage get icNotifications => const SvgGenImage('assets/images/svgs/icons/ic_notifications.svg');

  /// File path: assets/images/svgs/icons/ic_person.svg
  SvgGenImage get icPerson => const SvgGenImage('assets/images/svgs/icons/ic_person.svg');

  /// File path: assets/images/svgs/icons/ic_person_fill.svg
  SvgGenImage get icPersonFill => const SvgGenImage('assets/images/svgs/icons/ic_person_fill.svg');

  /// File path: assets/images/svgs/icons/ic_phone.svg
  SvgGenImage get icPhone => const SvgGenImage('assets/images/svgs/icons/ic_phone.svg');

  /// File path: assets/images/svgs/icons/ic_privacy_and_policy.svg
  SvgGenImage get icPrivacyAndPolicy => const SvgGenImage('assets/images/svgs/icons/ic_privacy_and_policy.svg');

  /// File path: assets/images/svgs/icons/ic_profile.svg
  SvgGenImage get icProfile => const SvgGenImage('assets/images/svgs/icons/ic_profile.svg');

  /// File path: assets/images/svgs/icons/ic_search.svg
  SvgGenImage get icSearch => const SvgGenImage('assets/images/svgs/icons/ic_search.svg');

  /// File path: assets/images/svgs/icons/ic_security.svg
  SvgGenImage get icSecurity => const SvgGenImage('assets/images/svgs/icons/ic_security.svg');

  /// File path: assets/images/svgs/icons/ic_send.svg
  SvgGenImage get icSend => const SvgGenImage('assets/images/svgs/icons/ic_send.svg');

  /// File path: assets/images/svgs/icons/ic_setting.svg
  SvgGenImage get icSetting => const SvgGenImage('assets/images/svgs/icons/ic_setting.svg');

  /// File path: assets/images/svgs/icons/ic_setting1.svg
  SvgGenImage get icSetting1 => const SvgGenImage('assets/images/svgs/icons/ic_setting1.svg');

  /// File path: assets/images/svgs/icons/ic_setting1_fill.svg
  SvgGenImage get icSetting1Fill => const SvgGenImage('assets/images/svgs/icons/ic_setting1_fill.svg');

  /// File path: assets/images/svgs/icons/ic_tremandcondtion.svg
  SvgGenImage get icTremandcondtion => const SvgGenImage('assets/images/svgs/icons/ic_tremandcondtion.svg');

  /// File path: assets/images/svgs/icons/ic_upload_file.svg
  SvgGenImage get icUploadFile => const SvgGenImage('assets/images/svgs/icons/ic_upload_file.svg');

  /// File path: assets/images/svgs/icons/ic_upload_image.svg
  SvgGenImage get icUploadImage => const SvgGenImage('assets/images/svgs/icons/ic_upload_image.svg');

  /// File path: assets/images/svgs/icons/ic_user.svg
  SvgGenImage get icUser => const SvgGenImage('assets/images/svgs/icons/ic_user.svg');

  /// File path: assets/images/svgs/icons/ic_wirite.svg
  SvgGenImage get icWirite => const SvgGenImage('assets/images/svgs/icons/ic_wirite.svg');

  /// File path: assets/images/svgs/icons/svg_close_coupon.svg
  SvgGenImage get svgCloseCoupon => const SvgGenImage('assets/images/svgs/icons/svg_close_coupon.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    icAlarm,
    icArrowRight,
    icBackArrow,
    icBeKind,
    icBlockAccount,
    icCalender,
    icChat,
    icChatFill,
    icClose,
    icDropdown,
    icEdit,
    icEyeOff,
    icEyeOn,
    icForwardArrow,
    icHeart,
    icHeartFill,
    icHelp,
    icHome,
    icHomeFill,
    icHomeTag,
    icKeepItReal,
    icLike,
    icLock,
    icLocks,
    icLogout,
    icMail,
    icMale,
    icMic,
    icNotification,
    icNotifications,
    icPerson,
    icPersonFill,
    icPhone,
    icPrivacyAndPolicy,
    icProfile,
    icSearch,
    icSecurity,
    icSend,
    icSetting,
    icSetting1,
    icSetting1Fill,
    icTremandcondtion,
    icUploadFile,
    icUploadImage,
    icUser,
    icWirite,
    svgCloseCoupon,
  ];
}

class $AssetsImagesSvgsOtherGen {
  const $AssetsImagesSvgsOtherGen();

  /// File path: assets/images/svgs/other/ic_profile_frame.svg
  SvgGenImage get icProfileFrame => const SvgGenImage('assets/images/svgs/other/ic_profile_frame.svg');

  /// List of all assets
  List<SvgGenImage> get values => [icProfileFrame];
}

class Assets {
  const Assets._();

  static const String aEnv = '.env';
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsLottieGen lottie = $AssetsLottieGen();

  /// List of all assets
  static List<String> get values => [aEnv];
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(_assetName, assetBundle: bundle, packageName: package);
    } else {
      loader = _svg.SvgAssetLoader(_assetName, assetBundle: bundle, packageName: package, theme: theme);
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ?? (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(this._assetName, {this.flavors = const {}});

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(BuildContext, Widget, _lottie.LottieComposition?)? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
    _lottie.LottieDecoder? decoder,
    _lottie.RenderCache? renderCache,
    bool? backgroundLoading,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
      decoder: decoder,
      renderCache: renderCache,
      backgroundLoading: backgroundLoading,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
