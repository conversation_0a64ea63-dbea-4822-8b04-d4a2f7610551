class UserData {
  final String id;
  final String name;
  final String imagePath;
  final bool isNewHere;
  final List<String> tags;
  final String description;
  final String role;
  final String bio;
  final String leasePeriod;
  final String frequency;
  final String level;
  final List<String> otherPhotos;

  UserData({
    required this.id,
    required this.name,
    required this.imagePath,
    required this.isNewHere,
    required this.tags,
    required this.description,
    required this.role,
    required this.bio,
    required this.leasePeriod,
    required this.frequency,
    required this.level,
    this.otherPhotos = const [],
  });
}
