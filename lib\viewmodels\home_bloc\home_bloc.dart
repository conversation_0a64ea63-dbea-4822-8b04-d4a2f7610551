import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:room_eight/models/home_model/user_model.dart';
import 'package:swipable_stack/swipable_stack.dart';
import 'package:room_eight/core/utils/hive_storage.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc()
    : super(
        HomeState(
          users: _sampleUsers,
          currentIndex: 0,
          pageController: PageController(),
          isPulseAnimating: false,
          carouselCurrentIndex: 0,
          hasSeenTutorial: true,
          swipableController: SwipableStackController(),
        ),
      ) {
    on<HomePageChanged>(_onPageChanged);
    on<HomeResetToFirst>(_onResetToFirst);
    on<AcceptUser>(_onAcceptUser);
    on<RejectUser>(_onRejectUser);
    on<StartPulseAnimation>(_onStartPulseAnimation);
    on<StopPulseAnimation>(_onStopPulseAnimation);
    on<CarouselPageChanged>(_onCarouselPageChanged);
    on<CheckTutorialStatus>(_onCheckTutorialStatus);
    on<DismissTutorial>(_onDismissTutorial);
    on<InitializeSwipableController>(_onInitializeSwipableController);
    on<DisposeSwipableController>(_onDisposeSwipableController);

    // Initialize tutorial status when bloc is created
    add(const CheckTutorialStatus());
  }

  void _onPageChanged(HomePageChanged event, Emitter<HomeState> emit) {
    emit(state.copyWith(currentIndex: event.index));
  }

  void _onResetToFirst(HomeResetToFirst event, Emitter<HomeState> emit) {
    if (state.currentIndex != 0) {
      state.pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      emit(state.copyWith(currentIndex: 0));
    }
  }

  void _onAcceptUser(AcceptUser event, Emitter<HomeState> emit) {
    final updatedUsers = state.users
        .where((u) => u.id != event.user.id)
        .toList();
    emit(state.copyWith(users: updatedUsers));
  }

  void _onRejectUser(RejectUser event, Emitter<HomeState> emit) {
    final updatedUsers = state.users
        .where((u) => u.id != event.user.id)
        .toList();
    emit(state.copyWith(users: updatedUsers));
  }

  void _onStartPulseAnimation(
    StartPulseAnimation event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(isPulseAnimating: true));
  }

  void _onStopPulseAnimation(
    StopPulseAnimation event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(isPulseAnimating: false));
  }

  void _onCarouselPageChanged(
    CarouselPageChanged event,
    Emitter<HomeState> emit,
  ) {
    emit(state.copyWith(carouselCurrentIndex: event.index));
  }

  Future<void> _onCheckTutorialStatus(
    CheckTutorialStatus event,
    Emitter<HomeState> emit,
  ) async {
    await Future.delayed(Duration.zero);
    final seen =
        Prefobj.preferences?.get(Prefkeys.SWIPE_TUTORIAL_SHOWN) ?? false;
    emit(state.copyWith(hasSeenTutorial: seen));
  }

  Future<void> _onDismissTutorial(
    DismissTutorial event,
    Emitter<HomeState> emit,
  ) async {
    await Prefobj.preferences?.put(Prefkeys.SWIPE_TUTORIAL_SHOWN, true);
    emit(state.copyWith(hasSeenTutorial: true));
  }

  void _onInitializeSwipableController(
    InitializeSwipableController event,
    Emitter<HomeState> emit,
  ) {
    final controller = SwipableStackController();
    emit(state.copyWith(swipableController: controller));
  }

  void _onDisposeSwipableController(
    DisposeSwipableController event,
    Emitter<HomeState> emit,
  ) {
    state.swipableController?.dispose();
    emit(state.copyWith(swipableController: null));
  }

  static final List<UserData> _sampleUsers = [
    UserData(
      id: '1',
      name: 'Cameron Williamson',
      imagePath: 'assets/images/pngs/other/png_home_user.png',
      isNewHere: true,
      tags: ['Occasionally', 'Rarely', 'Junior'],
      description:
          'I\'m photographer, yoga enthusiast, love to relax but very adventurers and lover for superhero movies.',
      role: 'Data Science',
      bio:
          'I\'m photographer, yoga enthusiast, love to relax but very adventurers and lover for superhero movies.',
      leasePeriod: '2024 - Jul 2025',
      frequency: 'Occasionally',
      level: 'Junior',
      otherPhotos: [
        'assets/images/pngs/other/png_home_user.png',
        'assets/images/pngs/other/png_home_user.png',
        'assets/images/pngs/other/png_home_user.png',
        'assets/images/pngs/other/png_home_user.png',
      ],
    ),

    UserData(
      id: '2',
      name: 'Maria Rodriguez',
      imagePath:
          'https://photosly.in/wp-content/uploads/2024/07/aesthetic-real-girl-pic_72.webp',
      isNewHere: false,
      tags: ['Fitness', 'Early Bird', 'Vegetarian', 'Often', '750/ Month'],
      description:
          'Yoga instructor who loves morning workouts. Plant-based lifestyle and sustainable living advocate.',
      role: 'Fitness Instructor',
      bio:
          'Certified yoga instructor with 5 years of experience. I start my day at 5 AM with meditation and workout. Passionate about plant-based nutrition and sustainable living. Looking for a room 8 who respects quiet morning hours and shares similar wellness values.',
      leasePeriod: '2024 - Jul 2025',
      frequency: 'Often',
      level: 'Senior',
      otherPhotos: [
        'https://photosly.in/wp-content/uploads/2024/07/aesthetic-real-girl-pic_72.webp',
        'https://photosly.in/wp-content/uploads/2024/07/aesthetic-real-girl-pic_72.webp',
        'https://photosly.in/wp-content/uploads/2024/07/aesthetic-real-girl-pic_72.webp',
        'https://photosly.in/wp-content/uploads/2024/07/aesthetic-real-girl-pic_72.webp',
        'https://photosly.in/wp-content/uploads/2024/07/aesthetic-real-girl-pic_72.webp',
        'https://photosly.in/wp-content/uploads/2024/07/aesthetic-real-girl-pic_72.webp',
      ],
    ),
    UserData(
      id: '3',
      name: 'Cameron Williamson',
      imagePath:
          'https://img.freepik.com/free-photo/portrait-curly-headed-man-wearing-casual-maroon-t-shirt_176532-8142.jpg',
      isNewHere: true,
      tags: ['Occasionally', 'Rarely', 'Junior'],
      description:
          'I\'m photographer, yoga enthusiast, love to relax but very adventurers and lover for superhero movies.',
      role: 'Data Science',
      bio:
          'I\'m photographer, yoga enthusiast, love to relax but very adventurers and lover for superhero movies.',
      leasePeriod: '2024 - Jul 2025',
      frequency: 'Occasionally',
      level: 'Junior',
      otherPhotos: [
        'https://img.freepik.com/free-photo/portrait-curly-headed-man-wearing-casual-maroon-t-shirt_176532-8142.jpg',
        'https://img.freepik.com/free-photo/portrait-curly-headed-man-wearing-casual-maroon-t-shirt_176532-8142.jpg',
        'https://img.freepik.com/free-photo/portrait-curly-headed-man-wearing-casual-maroon-t-shirt_176532-8142.jpg',
        'https://img.freepik.com/free-photo/portrait-curly-headed-man-wearing-casual-maroon-t-shirt_176532-8142.jpg',
      ],
    ),

    UserData(
      id: '4',
      name: 'Sarah Johnson',
      imagePath:
          'https://images.pexels.com/photos/1704488/pexels-photo-1704488.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
      isNewHere: false,
      tags: ['Clean', 'Organized', 'Social', 'Daily', '800/ Month'],
      description:
          'Love cooking and hosting friends. Looking for someone who appreciates a clean, organized living space.',
      role: 'Marketing Manager',
      bio:
          'Marketing manager at a tech startup. I love cooking elaborate meals and hosting dinner parties for friends. Super organized and believe a clean space equals a clear mind. Looking for a room 8 who shares the same values about cleanliness and enjoys socializing.',
      leasePeriod: '2024 - Jul 2025',
      frequency: 'Daily',
      level: 'Intermediate',
      otherPhotos: [
        'https://images.pexels.com/photos/1704488/pexels-photo-1704488.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        'https://images.pexels.com/photos/1704488/pexels-photo-1704488.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
        'https://images.pexels.com/photos/1704488/pexels-photo-1704488.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
      ],
    ),
    UserData(
      id: '5',
      name: 'Alex Chen',
      imagePath:
          'https://images.unsplash.com/photo-1669475535925-a011d7c31d45?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fHByb2ZpbGUlMjBpbWFnZXxlbnwwfHwwfHx8MA%3D%3D',
      isNewHere: true,
      tags: ['Quiet', 'Student', 'Night Owl', 'Weekly', '450/ Month'],
      description:
          'PhD student in Computer Science. Prefer quiet environments for studying. Love gaming and anime.',
      role: 'PhD Student',
      bio:
          'PhD student in Computer Science specializing in machine learning. I\'m a night owl who does my best work after 10 PM. Love gaming, anime, and deep tech discussions. Need a quiet space for research and study. Looking for a considerate room 8 who respects study time.',
      leasePeriod: '2024 - Jul 2025',
      frequency: 'Weekly',
      level: 'Intermediate',
      otherPhotos: [
        'https://images.unsplash.com/photo-1669475535925-a011d7c31d45?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fHByb2ZpbGUlMjBpbWFnZXxlbnwwfHwwfHx8MA%3D%3D',
        'https://images.unsplash.com/photo-1669475535925-a011d7c31d45?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fHByb2ZpbGUlMjBpbWFnZXxlbnwwfHwwfHx8MA%3D%3D',
      ],
    ),
    UserData(
      id: '6',
      name: 'James Wilson',
      imagePath:
          'https://images.unsplash.com/photo-1678286742832-26543bb49959?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlciUyMHByb2ZpbGV8ZW58MHx8MHx8fDA%3D',
      isNewHere: true,
      tags: ['Occasionally', 'Messy', 'Not Oky', 'Rarely', '600/ Month'],
      description:
          'True Detective is a gripping anthology series. exploring crime, morality, and human nature more',
      role: 'Film Critic',
      bio:
          'Freelance film critic and entertainment blogger. I spend most of my time watching movies and TV series, writing reviews. A bit disorganized but passionate about storytelling. Looking for someone who doesn\'t mind occasional mess and loves discussing movies late into the night.',
      leasePeriod: '2024 - Jul 2025',
      frequency: 'Occasionally',
      level: 'Intermediate',
      otherPhotos: [
        'https://images.unsplash.com/photo-1678286742832-26543bb49959?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlciUyMHByb2ZpbGV8ZW58MHx8MHx8fDA%3D',
        'https://images.unsplash.com/photo-1678286742832-26543bb49959?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlciUyMHByb2ZpbGV8ZW58MHx8MHx8fDA%3D',
      ],
    ),
  ];
}
