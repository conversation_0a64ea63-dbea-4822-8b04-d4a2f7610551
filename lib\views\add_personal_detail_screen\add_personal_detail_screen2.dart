import 'dart:ui';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

class AddPersonalDetailScreen2 extends StatelessWidget {
  AddPersonalDetailScreen2({super.key});

  static Widget builder(BuildContext context) => AddPersonalDetailScreen2();

  final List<String> genderList = ["Male", "Female", "Both"];

  final List<String> leasePeriods = ["9 months", "12 months"];

  final List<String> smokeLevel = [
    "Smoker",
    "Social Smoker",
    "Occasionally",
    "Non - Smoker",
  ];

  final List<String> clean = ["Messy", "Very Tidy", "Average"];

  final List<String> pate = ["Okay with Pets", "Not Okay"];

  final List<String> classStanding = [
    "Freshman",
    "Sophomore",
    "Junior",
    "Senior",
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).customColors.fillColor,
          body: BackgroundImage(
            imagePath: Assets.images.pngs.other.pngAuthBg3.path,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height,
              ),
              child: IntrinsicHeight(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildTopSection(context),
                    buildSizedBoxH(25.h),
                    Expanded(child: _buildBody(context, state)),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 60.h,
        bottom: 16.h,
      ),
      child: _buildTopBar(context),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CustomGradientContainer(
          height: 36.w,
          width: 36.w,
          topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
          bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
          fillColor: customColors.fillColor!.withAlpha(75),
          child: CustomImageView(
            imagePath: Assets.images.svgs.icons.icBackArrow.path,
          ),
        ),
      ],
    );
  }

  Widget _buildBody(BuildContext context, ProfileState state) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          decoration: BoxDecoration(
            color: Theme.of(
              context,
            ).customColors.fillColor?.withValues(alpha: 0.8),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDragHandle(context),
                buildSizedBoxH(24.h),
                Expanded(child: _buildDetails(context, state)),
                buildSizedBoxH(24.h),
                // const Spacer(),
                _buildProfileButton(context, state),
                // buildSizedBoxH(16.h),
                // _buildSignUpOption(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDragHandle(BuildContext context) {
    return Column(
      children: [
        Text(
          Lang.of(context).lbl_tell_us_about_your_self,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 22.sp,
            color: Theme.of(context).customColors.blackColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        buildSizedBoxH(4.h),
        Text(
          Lang.of(context).lbl_your_personal_details_desc2,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 14.sp,
            color: Theme.of(context).customColors.darkGreytextcolor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildDetails(BuildContext context, ProfileState state) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _builsLabel(context, Lang.of(context).lbl_preferred_lease_period),
          buildSizedBoxH(10.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              color: Colors.white,
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: state.selectedPeriod,
                isExpanded: true,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).customColors.darkGreytextcolor,
                  fontSize: 16.0.sp,
                  fontWeight: FontWeight.w500,
                ),
                items: leasePeriods.map((String period) {
                  return DropdownMenuItem<String>(
                    value: period,
                    child: Text(period),
                  );
                }).toList(),
                icon: Icon(Icons.keyboard_arrow_down),
                onChanged: (String? newValue) {
                  context.read<ProfileBloc>().add(
                    SelectPeriod(period: newValue ?? ''),
                  );
                },
              ),
            ),
          ),
          buildSizedBoxH(25.h),
          _buildPreferredLocationsField(context, state),
          buildSizedBoxH(25.h),
          _builsLabel(context, "What is your gender?"),
          buildSizedBoxH(10.h),
          buildSelectableChipsRow(
            context: context,
            options: genderList,
            selectedOption: state.selectedGender,
            onSelected: (value) {
              context.read<ProfileBloc>().add(SelectGender(gender: value));
            },
          ),
          buildSizedBoxH(25.h),
          _builsLabel(
            context,
            Lang.of(context).lbl_who_you_want_for_room_mate_seeing,
          ),
          buildSizedBoxH(10.h),
          buildSelectableChipsRow(
            context: context,
            options: genderList,
            selectedOption: state.selectedPreferredGender,
            onSelected: (value) {
              context.read<ProfileBloc>().add(
                SelectPreferredGender(preferredGender: value),
              );
            },
          ),
          buildSizedBoxH(25.h),
          _builsLabel(
            context,
            Lang.of(context).lbl_What_is_your_smoking_preference,
          ),
          buildSelectableChipsRow(
            context: context,
            options: smokeLevel,
            selectedOption: state.selectedSmokingPerson,
            onSelected: (value) {
              context.read<ProfileBloc>().add(SelectSmokingPerson(type: value));
            },
          ),
          buildSizedBoxH(25.h),
          _builsLabel(
            context,
            Lang.of(context).lbl_what_is_your_cleanliness_level,
          ),
          buildSelectableChipsRow(
            context: context,
            options: clean,
            selectedOption: state.sleectedCleanLevenl,
            onSelected: (value) {
              context.read<ProfileBloc>().add(SelectCleanLevel(level: value));
            },
          ),
          buildSizedBoxH(25.h),
          _builsLabel(context, Lang.of(context).lbl_do_you_have_any_pets),
          buildSelectableChipsRow(
            context: context,
            options: pate,
            selectedOption: state.selectedPet,
            onSelected: (value) {
              context.read<ProfileBloc>().add(SelectPet(pet: value));
            },
          ),
          buildSizedBoxH(25.h),
          _builsLabel(
            context,
            Lang.of(context).lbl_what_is_your_current_class_standing,
          ),
          buildSelectableChipsRow(
            context: context,
            options: classStanding,
            selectedOption: state.selectedClassStand,
            onSelected: (value) {
              context.read<ProfileBloc>().add(
                SelectClassStand(classStand: value),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPreferredLocationsField(
    BuildContext context,
    ProfileState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _builsLabel(context, Lang.of(context).lbl_preferred_locations),
        buildSizedBoxH(10.h),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          hintLabel: Lang.of(context).lbl_enter_preferred_locations,
          controller: state.preferredLocationsController,
          textInputAction: TextInputAction.next,
          onChanged: (value) => context.read<ProfileBloc>().add(
            SelectPreferredLocations(locations: value),
          ),
        ),
      ],
    );
  }

  Widget buildSelectableChipsRow({
    required BuildContext context,
    required List<String> options,
    required String? selectedOption,
    required ValueChanged<String> onSelected,
  }) {
    return Wrap(
      children: List.generate(
        options.length,
        (index) => Padding(
          padding: EdgeInsets.only(right: 5.w),
          child: _buildOptionDesign(
            context: context,
            text: options[index],
            isSelected: options[index] == selectedOption,
            onTap: () => onSelected(options[index]),
          ),
        ),
      ),
    );
  }

  Widget _buildOptionDesign({
    required BuildContext context,
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Chip(
        label: Text(
          text,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: isSelected
                ? Theme.of(context).customColors.fillColor
                : Theme.of(context).customColors.darkGreytextcolor,
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: isSelected
            ? Theme.of(context).customColors.primaryColor
            : Theme.of(context).customColors.fillColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.r), // Change radius as needed
          side: BorderSide(color: Colors.transparent, width: 0), // No border
        ),
      ),
    );
  }

  Widget _builsLabel(BuildContext context, String label) {
    return Text(
      label,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontSize: 16.sp,
        color: Theme.of(context).customColors.blackColor,
      ),
    );
  }

  Widget _buildProfileButton(BuildContext context, ProfileState state) {
    return CustomElevatedButton(
      isLoading: state.isloginLoading,
      isDisabled: state.isloginLoading,
      text: Lang.of(context).lbl_continue,
      buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
        color: Theme.of(context).customColors.fillColor,
        fontSize: 18.0.sp,
        fontWeight: FontWeight.w500,
      ),
      onPressed: () {
        // FocusManager.instance.primaryFocus?.unfocus();
        // if (state.addPersonalDetailFormKey.currentState?.validate() ?? false) {
        //   context.read<ProfileBloc>().add(ProfileSubmitted());
        // }
        NavigatorService.pushNamed(AppRoutes.addPersonalDetailScreen3);
      },
    );
  }
}
