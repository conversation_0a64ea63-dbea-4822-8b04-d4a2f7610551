import 'package:room_eight/core/utils/app_exports.dart';

class RoomEightApp extends StatelessWidget {
  const RoomEightApp({super.key, required this.prefs});
  final Box prefs;

  @override
  Widget build(BuildContext context) {
    Prefobj.preferences = prefs;
    FlutterNativeSplash.remove();
    return BlocProviders(
      child: MultiBlocListener(
        listeners: [
          BlocListener<CheckConnectionCubit, CheckConnectionStates>(
            listener: (context, state) {
              if (state is InternetDisconnected) {
                CheckConnectionCubit.get(context).isNetDialogShow = true;
                Logger.lOG('InternetDisconnected');
                Future.delayed(const Duration(milliseconds: 500), () {});
              }
              if (state is InternetConnected) {
                if (CheckConnectionCubit.get(context).isNetDialogShow) {
                  Logger.lOG('InternetConnected');

                  CheckConnectionCubit.get(context).isNetDialogShow = false;
                }
              }
            },
          ),
        ],
        child: <PERSON><PERSON><PERSON>er<LocaleBloc, LocaleState>(
          builder: (context, localeState) {
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                highContrast: true,
                displayFeatures: MediaQuery.of(context).displayFeatures,
                gestureSettings: MediaQuery.of(context).gestureSettings,
                textScaler: TextScaler.noScaling,
                invertColors: false,
                boldText: false,
              ),
              child: GestureDetector(
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                child: ScreenUtilInit(
                  designSize: Size(430, 932),
                  minTextAdapt: true,
                  splitScreenMode: true,
                  child: ToastificationWrapper(
                    child: MaterialApp(
                      title: 'Room 8',
                      builder: OneContext().builder,
                      debugShowCheckedModeBanner: false,
                      localizationsDelegates: const [
                        Lang.delegate,
                        GlobalMaterialLocalizations.delegate,
                        GlobalWidgetsLocalizations.delegate,
                        GlobalCupertinoLocalizations.delegate,
                      ],
                      supportedLocales: Lang.delegate.supportedLocales,
                      navigatorKey: NavigatorService.navigatorKey,
                      locale: localeState.locale,
                      theme: MyAppThemeHelper.lightTheme,
                      themeMode: ThemeMode.light,
                      initialRoute: AppRoutes.initialRoute,
                      routes: AppRoutes.routes,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
